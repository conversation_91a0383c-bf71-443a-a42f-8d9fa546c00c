/* Explore Container */
.explore-container {
  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Category Filter */
.category-filter {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-outset);
}

.category-scroll {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  scrollbar-width: none;
  padding: 4px 0;
}

.category-scroll::-webkit-scrollbar {
  display: none;
}

.category-btn {
  background: var(--bg-primary);
  border: none;
  border-radius: var(--radius-full);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: var(--shadow-outset);
}

.category-btn:hover {
  color: var(--accent-primary);
  transform: translateY(-1px);
  box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.2),
              -3px -3px 6px rgba(255, 255, 255, 0.1);
}

.category-btn.active {
  background: var(--accent-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-inset);
}

/* Posts Grid */
.posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.grid-post {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-outset);
  transition: all 0.3s ease;
  cursor: pointer;
}

.grid-post:hover {
  transform: translateY(-4px);
  box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.3),
              -6px -6px 12px rgba(255, 255, 255, 0.1);
}

.post-image-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  overflow: hidden;
}

.grid-post-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.grid-post:hover .grid-post-image {
  transform: scale(1.05);
}

.video-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: 12px;
}

.post-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.grid-post:hover .post-overlay {
  opacity: 1;
}

.post-stats {
  display: flex;
  gap: 20px;
  color: var(--text-primary);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
}

.stat-item svg {
  font-size: 16px;
}

/* Load More */
.load-more-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.load-more-btn {
  background: var(--accent-primary);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-md);
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-outset);
}

.load-more-btn:hover {
  background: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.2),
              -4px -4px 8px rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .category-filter {
    margin: 16px 0;
    padding: 12px;
  }
  
  .category-btn {
    font-size: 13px;
    padding: 6px 12px;
  }
  
  .posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 30px;
  }
  
  .post-stats {
    gap: 16px;
  }
  
  .stat-item {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .posts-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .category-scroll {
    gap: 8px;
  }
  
  .category-btn {
    font-size: 12px;
    padding: 6px 10px;
  }
  
  .post-stats {
    gap: 12px;
  }
  
  .stat-item {
    font-size: 12px;
  }
  
  .stat-item svg {
    font-size: 14px;
  }
} 
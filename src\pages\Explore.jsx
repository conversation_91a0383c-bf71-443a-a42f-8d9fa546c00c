import React, { useState } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>, Fa<PERSON>ye } from 'react-icons/fa';
import './Explore.css';

const Explore = () => {
  const [posts] = useState([
    {
      id: 1,
      image: '/IMG-20250622-WA0003.jpg',
      likes: 1234,
      comments: 56,
      views: 12500,
      isVideo: false
    },
    {
      id: 2,
      image: '/VID-20250622-WA0001.mp4',
      likes: 892,
      comments: 23,
      views: 8900,
      isVideo: true
    },
    {
      id: 3,
      image: '/VID-20250622-WA0002.mp4',
      likes: 2156,
      comments: 89,
      views: 15600,
      isVideo: true
    },
    {
      id: 4,
      image: '/VID-20250622-WA0003.mp4',
      likes: 567,
      comments: 12,
      views: 6700,
      isVideo: true
    },
    {
      id: 5,
      image: '/VID-20250622-WA0004.mp4',
      likes: 3421,
      comments: 156,
      views: 28900,
      isVideo: true
    },
    {
      id: 6,
      image: '/VID-20250622-WA0005.mp4',
      likes: 789,
      comments: 34,
      views: 9800,
      isVideo: true
    },
    {
      id: 7,
      image: '/VID-20250622-WA0006.mp4',
      likes: 1567,
      comments: 78,
      views: 18400,
      isVideo: true
    },
    {
      id: 8,
      image: '/VID-20250622-WA0007.mp4',
      likes: 2890,
      comments: 145,
      views: 25600,
      isVideo: true
    },
    {
      id: 9,
      image: '/VID-20250622-WA0008.mp4',
      likes: 3456,
      comments: 189,
      views: 32100,
      isVideo: true
    }
  ]);

  const [categories] = useState([
    { id: 1, name: 'Technology', active: true },
    { id: 2, name: 'Design', active: false },
    { id: 3, name: 'Photography', active: false },
    { id: 4, name: 'Art', active: false },
    { id: 5, name: 'Music', active: false },
    { id: 6, name: 'Travel', active: false }
  ]);

  return (
    <div className="explore-container">
      <div className="content-container">
        {/* Category Filter */}
        <div className="category-filter neomorphic-card">
          <div className="category-scroll">
            {categories.map(category => (
              <button
                key={category.id}
                className={`category-btn ${category.active ? 'active' : ''}`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Posts Grid */}
        <div className="posts-grid">
          {posts.map(post => (
            <div key={post.id} className="grid-post neomorphic-card">
              <div className="post-image-container">
                {post.isVideo ? (
                  <video
                    src={post.image}
                    alt="Post"
                    className="grid-post-image"
                    muted
                    preload="metadata"
                    onMouseEnter={(e) => e.target.play()}
                    onMouseLeave={(e) => e.target.pause()}
                  />
                ) : (
                  <img
                    src={post.image}
                    alt="Post"
                    className="grid-post-image"
                  />
                )}
                {post.isVideo && (
                  <div className="video-indicator">
                    <i className="fas fa-play"></i>
                  </div>
                )}
                <div className="post-overlay">
                  <div className="post-stats">
                    <div className="stat-item">
                      <FaHeart />
                      <span>{post.likes.toLocaleString()}</span>
                    </div>
                    <div className="stat-item">
                      <FaRegComment />
                      <span>{post.comments}</span>
                    </div>
                    <div className="stat-item">
                      <FaEye />
                      <span>{post.views.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More */}
        <div className="load-more-container">
          <button className="load-more-btn btn">
            Load More
          </button>
        </div>
      </div>
    </div>
  );
};

export default Explore; 
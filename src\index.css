@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Color Scheme */
  --primary-bg: #030f0f;
  --secondary-bg: #082525;
  --accent-color: #33BD94;
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --shadow-light: rgba(255, 255, 255, 0.05);
  --shadow-dark: rgba(0, 0, 0, 0.5);
  --input-bg: #121d1d;
  --border-color: rgba(255, 255, 255, 0.1);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--primary-bg);
  color: var(--text-primary);
  min-height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 60px; /* Height of the navbar */
  padding-bottom: 60px; /* Height of the tab bar */
}

/* Navigation styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: var(--secondary-bg);
  z-index: 1000;
}

.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: var(--secondary-bg);
  z-index: 1000;
}

/* New post button */
.new-post-btn {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: var(--accent-color);
  color: white;
  font-size: 24px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

.new-post-btn:hover {
  transform: scale(1.05);
}

/* Overlay styles */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-color);
  opacity: 0.8;
}

/* Button Reset */
button {
  font-family: inherit;
}

/* Link Reset */
a {
  text-decoration: none;
  color: inherit;
}

/* Input Reset */
input,
textarea {
  font-family: inherit;
}

/* Image Reset */
img {
  max-width: 100%;
  height: auto;
}

/* Focus Styles */
:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Neomorphic Styles */
.neomorphic {
  background: var(--secondary-bg);
  border-radius: 15px;
  box-shadow: 
    4px 4px 8px var(--shadow-dark),
    -2px -2px 6px var(--shadow-light);
  border: 1px solid var(--border-color);
}

.neomorphic-inset {
  background: var(--secondary-bg);
  border-radius: 15px;
  box-shadow: 
    inset 4px 4px 8px var(--shadow-dark),
    inset -2px -2px 6px var(--shadow-light);
  border: 1px solid var(--border-color);
}

/* Media Queries */
@media (max-width: 768px) {
  .main-content {
    padding-bottom: 80px;
  }
}

@media (min-width: 769px) {
  .new-post-btn {
    bottom: 30px;
  }
} 